import { Link } from "react-router-dom";
import { useC<PERSON>k, useUser } from "@clerk/clerk-react";

export default function LandingPage() {
  const { openUserProfile, signOut } = useClerk();
  const { isSignedIn, user } = useUser();

  return (
    <div>
      {/* Navbar */}
      <header className="p-4 flex justify-between bg-gray-900 text-white">
        <Link to="/" className="text-xl font-bold">
          My App
        </Link>

        <div className="flex items-center gap-4">
          {!isSignedIn ? (
            <>
              <Link
                to="/login"
                className="px-4 py-2 rounded bg-blue-500 hover:bg-blue-600"
              >
                Sign In
              </Link>
              <Link
                to="/signup"
                className="px-4 py-2 rounded bg-green-500 hover:bg-green-600"
              >
                Sign Up
              </Link>
            </>
          ) : (
            <>
              <span>Hello, {user?.firstName || user?.username || "User"}!</span>

              <button
                onClick={() => openUserProfile()}
                className="px-3 py-1 rounded bg-gray-700 hover:bg-gray-800"
              >
                Profile
              </button>

              <button
                onClick={() => signOut()}
                className="px-3 py-1 rounded bg-red-500 hover:bg-red-600"
              >
                Sign Out
              </button>
            </>
          )}
        </div>
      </header>

      {/* Page Content */}
      <main className="p-6">
        <h1 className="text-3xl font-bold">Welcome to My App</h1>
        <p className="mt-2 text-gray-600">
          This is the landing page. Use the navbar to navigate.
        </p>
      </main>
    </div>
  );
}
